import React, { Suspense, lazy } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'

import { useAuth } from './hooks/useAuth'
import Navbar from './components/Navbar'
import Footer from './components/Footer'
import Loader from './components/Loader'
import ProtectedRoute from './components/ProtectedRoute'

// Lazy load pages for better performance
const Home = lazy(() => import('./pages/Home'))
const ProductList = lazy(() => import('./pages/ProductList'))
const ProductDetail = lazy(() => import('./pages/ProductDetail'))
const Cart = lazy(() => import('./pages/Cart'))
const Checkout = lazy(() => import('./pages/Checkout'))
const Orders = lazy(() => import('./pages/Orders'))
const Donations = lazy(() => import('./pages/Donations'))
const PujaBooking = lazy(() => import('./pages/PujaBooking'))
const Profile = lazy(() => import('./pages/Profile'))
const AdminDashboard = lazy(() => import('./pages/AdminDashboard'))
const Login = lazy(() => import('./pages/auth/Login'))
const Register = lazy(() => import('./pages/auth/Register'))
const ForgotPassword = lazy(() => import('./pages/auth/ForgotPassword'))
const NotFound = lazy(() => import('./pages/NotFound'))

// Loading component for Suspense
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <Loader size="lg" />
  </div>
)

// Layout component
const Layout = ({ children }) => (
  <div className="min-h-screen flex flex-col bg-neutral-50">
    <Navbar />
    <main className="flex-1">
      {children}
    </main>
    <Footer />
  </div>
)

// Auth layout (without navbar/footer)
const AuthLayout = ({ children }) => (
  <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
    {children}
  </div>
)

function App() {
  const { user, isLoading } = useAuth()

  // Show loading screen while checking authentication
  if (isLoading) {
    return <PageLoader />
  }

  return (
    <>
      <Helmet>
        <title>Worship E-Commerce - Sacred Products & Spiritual Services</title>
        <meta name="description" content="Discover authentic spiritual products, book puja services, and make donations. Your one-stop destination for all worship needs." />
      </Helmet>

      <Routes>
        {/* Public Routes */}
        <Route path="/" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Home />
            </Suspense>
          </Layout>
        } />

        <Route path="/products" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <ProductList />
            </Suspense>
          </Layout>
        } />

        <Route path="/products/:id" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <ProductDetail />
            </Suspense>
          </Layout>
        } />

        <Route path="/cart" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Cart />
            </Suspense>
          </Layout>
        } />

        <Route path="/donations" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Donations />
            </Suspense>
          </Layout>
        } />

        <Route path="/puja-booking" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <PujaBooking />
            </Suspense>
          </Layout>
        } />

        {/* Auth Routes */}
        <Route path="/login" element={
          user ? <Navigate to="/" replace /> : (
            <AuthLayout>
              <Suspense fallback={<PageLoader />}>
                <Login />
              </Suspense>
            </AuthLayout>
          )
        } />

        <Route path="/register" element={
          user ? <Navigate to="/" replace /> : (
            <AuthLayout>
              <Suspense fallback={<PageLoader />}>
                <Register />
              </Suspense>
            </AuthLayout>
          )
        } />

        <Route path="/forgot-password" element={
          user ? <Navigate to="/" replace /> : (
            <AuthLayout>
              <Suspense fallback={<PageLoader />}>
                <ForgotPassword />
              </Suspense>
            </AuthLayout>
          )
        } />

        {/* Protected Routes */}
        <Route path="/checkout" element={
          <ProtectedRoute>
            <Layout>
              <Suspense fallback={<PageLoader />}>
                <Checkout />
              </Suspense>
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/orders" element={
          <ProtectedRoute>
            <Layout>
              <Suspense fallback={<PageLoader />}>
                <Orders />
              </Suspense>
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/profile" element={
          <ProtectedRoute>
            <Layout>
              <Suspense fallback={<PageLoader />}>
                <Profile />
              </Suspense>
            </Layout>
          </ProtectedRoute>
        } />

        {/* Admin Routes */}
        <Route path="/admin/*" element={
          <ProtectedRoute requiredRole="admin">
            <Suspense fallback={<PageLoader />}>
              <AdminDashboard />
            </Suspense>
          </ProtectedRoute>
        } />

        {/* Festival Routes */}
        <Route path="/festivals/diwali" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <ProductList festival="diwali" />
            </Suspense>
          </Layout>
        } />

        <Route path="/festivals/navratri" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <ProductList festival="navratri" />
            </Suspense>
          </Layout>
        } />

        <Route path="/festivals/ganesh-chaturthi" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <ProductList festival="ganesh_chaturthi" />
            </Suspense>
          </Layout>
        } />

        {/* Category Routes */}
        <Route path="/category/:category" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <ProductList />
            </Suspense>
          </Layout>
        } />

        {/* Deity Routes */}
        <Route path="/deity/:deity" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <ProductList />
            </Suspense>
          </Layout>
        } />

        {/* Search Route */}
        <Route path="/search" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <ProductList />
            </Suspense>
          </Layout>
        } />

        {/* 404 Route */}
        <Route path="*" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <NotFound />
            </Suspense>
          </Layout>
        } />
      </Routes>
    </>
  )
}

export default App
