const config = require('../config/env');

/**
 * Format price for display
 * @param {Number} price - Price in smallest currency unit (paise for INR, cents for USD)
 * @param {String} currency - Currency code (INR, USD)
 * @param {String} locale - Locale for formatting
 * @returns {String} Formatted price string
 */
const formatPrice = (price, currency = 'INR', locale = 'en-IN') => {
  try {
    if (typeof price !== 'number' || isNaN(price)) {
      return '₹0';
    }

    // Convert from smallest unit to main unit
    const mainUnitPrice = currency === 'INR' ? price / 100 : price / 100;

    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });

    return formatter.format(mainUnitPrice);
  } catch (error) {
    // Fallback formatting
    if (currency === 'INR') {
      return `₹${(price / 100).toFixed(2)}`;
    } else {
      return `$${(price / 100).toFixed(2)}`;
    }
  }
};

/**
 * Format price in Indian numbering system (lakhs, crores)
 * @param {Number} price - Price in smallest currency unit
 * @param {Boolean} showDecimals - Whether to show decimal places
 * @returns {String} Formatted price string
 */
const formatPriceIndian = (price, showDecimals = true) => {
  try {
    if (typeof price !== 'number' || isNaN(price)) {
      return '₹0';
    }

    const mainUnitPrice = price / 100;
    
    if (mainUnitPrice >= 10000000) { // 1 crore
      const crores = mainUnitPrice / 10000000;
      return `₹${crores.toFixed(showDecimals ? 2 : 0)} Cr`;
    } else if (mainUnitPrice >= 100000) { // 1 lakh
      const lakhs = mainUnitPrice / 100000;
      return `₹${lakhs.toFixed(showDecimals ? 2 : 0)} L`;
    } else if (mainUnitPrice >= 1000) { // 1 thousand
      const thousands = mainUnitPrice / 1000;
      return `₹${thousands.toFixed(showDecimals ? 1 : 0)}K`;
    } else {
      return `₹${mainUnitPrice.toFixed(showDecimals ? 2 : 0)}`;
    }
  } catch (error) {
    return `₹${(price / 100).toFixed(2)}`;
  }
};

/**
 * Parse price string to number (in smallest currency unit)
 * @param {String} priceString - Price string
 * @param {String} currency - Currency code
 * @returns {Number} Price in smallest currency unit
 */
const parsePrice = (priceString, currency = 'INR') => {
  try {
    if (typeof priceString === 'number') {
      return Math.round(priceString * 100); // Convert to smallest unit
    }

    if (typeof priceString !== 'string') {
      return 0;
    }

    // Remove currency symbols and spaces
    const cleanString = priceString
      .replace(/[₹$,\s]/g, '')
      .replace(/[^\d.]/g, '');

    const price = parseFloat(cleanString);
    
    if (isNaN(price)) {
      return 0;
    }

    // Convert to smallest currency unit
    return Math.round(price * 100);
  } catch (error) {
    return 0;
  }
};

/**
 * Calculate discount percentage
 * @param {Number} originalPrice - Original price
 * @param {Number} salePrice - Sale price
 * @returns {Number} Discount percentage
 */
const calculateDiscountPercentage = (originalPrice, salePrice) => {
  try {
    if (typeof originalPrice !== 'number' || typeof salePrice !== 'number') {
      return 0;
    }

    if (originalPrice <= 0 || salePrice <= 0 || salePrice >= originalPrice) {
      return 0;
    }

    const discount = ((originalPrice - salePrice) / originalPrice) * 100;
    return Math.round(discount);
  } catch (error) {
    return 0;
  }
};

/**
 * Calculate discount amount
 * @param {Number} originalPrice - Original price
 * @param {Number} salePrice - Sale price
 * @returns {Number} Discount amount
 */
const calculateDiscountAmount = (originalPrice, salePrice) => {
  try {
    if (typeof originalPrice !== 'number' || typeof salePrice !== 'number') {
      return 0;
    }

    if (originalPrice <= 0 || salePrice <= 0 || salePrice >= originalPrice) {
      return 0;
    }

    return originalPrice - salePrice;
  } catch (error) {
    return 0;
  }
};

/**
 * Calculate tax amount
 * @param {Number} price - Price before tax
 * @param {Number} taxPercentage - Tax percentage
 * @returns {Number} Tax amount
 */
const calculateTax = (price, taxPercentage = config.GST_PERCENTAGE) => {
  try {
    if (typeof price !== 'number' || typeof taxPercentage !== 'number') {
      return 0;
    }

    if (price <= 0 || taxPercentage <= 0) {
      return 0;
    }

    return Math.round((price * taxPercentage) / 100);
  } catch (error) {
    return 0;
  }
};

/**
 * Calculate price with tax
 * @param {Number} price - Price before tax
 * @param {Number} taxPercentage - Tax percentage
 * @returns {Number} Price including tax
 */
const calculatePriceWithTax = (price, taxPercentage = config.GST_PERCENTAGE) => {
  try {
    const tax = calculateTax(price, taxPercentage);
    return price + tax;
  } catch (error) {
    return price;
  }
};

/**
 * Calculate shipping cost
 * @param {Number} orderValue - Order value
 * @param {Number} weight - Package weight
 * @param {String} location - Delivery location
 * @returns {Number} Shipping cost
 */
const calculateShippingCost = (orderValue, weight = 0, location = 'domestic') => {
  try {
    // Free shipping for orders above threshold
    if (orderValue >= config.FREE_DELIVERY_THRESHOLD * 100) {
      return 0;
    }

    let shippingCost = config.DELIVERY_CHARGE * 100; // Convert to smallest unit

    // Weight-based pricing
    if (weight > 1) { // More than 1 kg
      shippingCost += Math.ceil(weight - 1) * 20 * 100; // ₹20 per additional kg
    }

    // Location-based pricing
    if (location === 'international') {
      shippingCost *= 5; // 5x for international shipping
    } else if (location === 'remote') {
      shippingCost *= 1.5; // 1.5x for remote locations
    }

    return Math.round(shippingCost);
  } catch (error) {
    return config.DELIVERY_CHARGE * 100;
  }
};

/**
 * Calculate platform fee
 * @param {Number} orderValue - Order value
 * @returns {Number} Platform fee
 */
const calculatePlatformFee = (orderValue) => {
  try {
    if (typeof orderValue !== 'number' || orderValue <= 0) {
      return 0;
    }

    return Math.round((orderValue * config.PLATFORM_FEE_PERCENTAGE) / 100);
  } catch (error) {
    return 0;
  }
};

/**
 * Calculate order total
 * @param {Array} items - Order items
 * @param {Object} options - Calculation options
 * @returns {Object} Order total breakdown
 */
const calculateOrderTotal = (items, options = {}) => {
  try {
    const {
      shippingWeight = 0,
      shippingLocation = 'domestic',
      taxPercentage = config.GST_PERCENTAGE,
      applyPlatformFee = false
    } = options;

    let subtotal = 0;
    let totalDiscount = 0;

    // Calculate subtotal and discount
    items.forEach(item => {
      const itemTotal = item.price * item.quantity;
      subtotal += itemTotal;

      if (item.originalPrice && item.originalPrice > item.price) {
        totalDiscount += (item.originalPrice - item.price) * item.quantity;
      }
    });

    // Calculate shipping
    const shippingCost = calculateShippingCost(subtotal, shippingWeight, shippingLocation);

    // Calculate tax on subtotal + shipping
    const taxableAmount = subtotal + shippingCost;
    const tax = calculateTax(taxableAmount, taxPercentage);

    // Calculate platform fee
    const platformFee = applyPlatformFee ? calculatePlatformFee(subtotal) : 0;

    // Calculate total
    const total = subtotal + shippingCost + tax + platformFee;

    return {
      subtotal,
      discount: totalDiscount,
      shippingCost,
      tax,
      platformFee,
      total,
      savings: totalDiscount,
      breakdown: {
        itemsTotal: subtotal,
        discount: totalDiscount,
        shipping: shippingCost,
        tax: tax,
        platformFee: platformFee,
        grandTotal: total
      }
    };
  } catch (error) {
    return {
      subtotal: 0,
      discount: 0,
      shippingCost: 0,
      tax: 0,
      platformFee: 0,
      total: 0,
      savings: 0,
      breakdown: {}
    };
  }
};

/**
 * Validate price
 * @param {Number} price - Price to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validation result
 */
const validatePrice = (price, options = {}) => {
  const { min = 0, max = 10000000, currency = 'INR' } = options;

  const result = {
    isValid: true,
    errors: []
  };

  if (typeof price !== 'number') {
    result.isValid = false;
    result.errors.push('Price must be a number');
    return result;
  }

  if (isNaN(price)) {
    result.isValid = false;
    result.errors.push('Price must be a valid number');
    return result;
  }

  if (price < min) {
    result.isValid = false;
    result.errors.push(`Price must be at least ${formatPrice(min, currency)}`);
  }

  if (price > max) {
    result.isValid = false;
    result.errors.push(`Price cannot exceed ${formatPrice(max, currency)}`);
  }

  return result;
};

module.exports = {
  formatPrice,
  formatPriceIndian,
  parsePrice,
  calculateDiscountPercentage,
  calculateDiscountAmount,
  calculateTax,
  calculatePriceWithTax,
  calculateShippingCost,
  calculatePlatformFee,
  calculateOrderTotal,
  validatePrice
};
