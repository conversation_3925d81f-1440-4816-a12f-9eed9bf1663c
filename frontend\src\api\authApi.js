import axios from 'axios'

// Create axios instance with default config
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  withCredentials: true,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Log response time in development
    if (import.meta.env.DEV) {
      const endTime = new Date()
      const duration = endTime - response.config.metadata.startTime
      console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`)
    }
    
    return response.data
  },
  async (error) => {
    const originalRequest = error.config
    
    // Handle token refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      try {
        await api.post('/auth/refresh-token')
        return api(originalRequest)
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }
    
    // Handle network errors
    if (!error.response) {
      return Promise.reject({
        message: 'Network error. Please check your connection.',
        type: 'network'
      })
    }
    
    // Handle server errors
    if (error.response.status >= 500) {
      return Promise.reject({
        message: 'Server error. Please try again later.',
        type: 'server',
        status: error.response.status
      })
    }
    
    return Promise.reject(error)
  }
)

// Auth API methods
export const authApi = {
  // Register new user
  register: async (userData) => {
    try {
      const response = await api.post('/auth/register', userData)
      
      // Store token if provided
      if (response.data?.tokens?.accessToken) {
        localStorage.setItem('accessToken', response.data.tokens.accessToken)
      }
      
      return response
    } catch (error) {
      throw error
    }
  },

  // Login user
  login: async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials)
      
      // Store token if provided
      if (response.data?.tokens?.accessToken) {
        localStorage.setItem('accessToken', response.data.tokens.accessToken)
      }
      
      return response
    } catch (error) {
      throw error
    }
  },

  // Logout user
  logout: async () => {
    try {
      const response = await api.post('/auth/logout')
      
      // Clear stored token
      localStorage.removeItem('accessToken')
      
      return response
    } catch (error) {
      // Clear token even if logout fails
      localStorage.removeItem('accessToken')
      throw error
    }
  },

  // Get user profile
  getProfile: async () => {
    try {
      return await api.get('/auth/profile')
    } catch (error) {
      throw error
    }
  },

  // Update user profile
  updateProfile: async (profileData) => {
    try {
      return await api.put('/auth/profile', profileData)
    } catch (error) {
      throw error
    }
  },

  // Change password
  changePassword: async (passwordData) => {
    try {
      const response = await api.put('/auth/change-password', passwordData)
      
      // Clear token after password change
      localStorage.removeItem('accessToken')
      
      return response
    } catch (error) {
      throw error
    }
  },

  // Refresh access token
  refreshToken: async () => {
    try {
      const response = await api.post('/auth/refresh-token')
      
      // Update stored token
      if (response.data?.tokens?.accessToken) {
        localStorage.setItem('accessToken', response.data.tokens.accessToken)
      }
      
      return response
    } catch (error) {
      throw error
    }
  },

  // Forgot password
  forgotPassword: async (email) => {
    try {
      return await api.post('/auth/forgot-password', { email })
    } catch (error) {
      throw error
    }
  },

  // Reset password
  resetPassword: async (token, password) => {
    try {
      return await api.post('/auth/reset-password', { token, password })
    } catch (error) {
      throw error
    }
  },

  // Verify email
  verifyEmail: async (token) => {
    try {
      return await api.post('/auth/verify-email', { token })
    } catch (error) {
      throw error
    }
  },

  // Resend verification email
  resendVerification: async () => {
    try {
      return await api.post('/auth/resend-verification')
    } catch (error) {
      throw error
    }
  },

  // Address management
  addAddress: async (addressData) => {
    try {
      return await api.post('/auth/addresses', addressData)
    } catch (error) {
      throw error
    }
  },

  updateAddress: async (addressId, addressData) => {
    try {
      return await api.put(`/auth/addresses/${addressId}`, addressData)
    } catch (error) {
      throw error
    }
  },

  deleteAddress: async (addressId) => {
    try {
      return await api.delete(`/auth/addresses/${addressId}`)
    } catch (error) {
      throw error
    }
  },

  // Social authentication
  googleAuth: async (token) => {
    try {
      const response = await api.post('/auth/google', { token })
      
      // Store token if provided
      if (response.data?.tokens?.accessToken) {
        localStorage.setItem('accessToken', response.data.tokens.accessToken)
      }
      
      return response
    } catch (error) {
      throw error
    }
  },

  // Check if email exists
  checkEmail: async (email) => {
    try {
      return await api.post('/auth/check-email', { email })
    } catch (error) {
      throw error
    }
  },

  // Check if phone exists
  checkPhone: async (phone) => {
    try {
      return await api.post('/auth/check-phone', { phone })
    } catch (error) {
      throw error
    }
  }
}

export default api
