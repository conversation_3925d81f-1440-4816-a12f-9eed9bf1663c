<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/om-symbol.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Primary Meta Tags -->
    <title>Worship E-Commerce - Sacred Products & Spiritual Services</title>
    <meta name="title" content="Worship E-Commerce - Sacred Products & Spiritual Services" />
    <meta name="description" content="Discover authentic spiritual products, book puja services, and make donations. Your one-stop destination for all worship needs with genuine products and trusted services." />
    <meta name="keywords" content="worship, spiritual products, puja services, donations, idols, incense, temple items, religious books, spiritual gifts" />
    <meta name="author" content="Worship E-Commerce Team" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://worship-ecommerce.vercel.app/" />
    <meta property="og:title" content="Worship E-Commerce - Sacred Products & Spiritual Services" />
    <meta property="og:description" content="Discover authentic spiritual products, book puja services, and make donations. Your one-stop destination for all worship needs." />
    <meta property="og:image" content="/og-image.jpg" />
    <meta property="og:site_name" content="Worship E-Commerce" />
    <meta property="og:locale" content="en_US" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://worship-ecommerce.vercel.app/" />
    <meta property="twitter:title" content="Worship E-Commerce - Sacred Products & Spiritual Services" />
    <meta property="twitter:description" content="Discover authentic spiritual products, book puja services, and make donations. Your one-stop destination for all worship needs." />
    <meta property="twitter:image" content="/twitter-image.jpg" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#d97706" />
    <meta name="msapplication-TileColor" content="#d97706" />
    <meta name="theme-color" content="#d97706" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://res.cloudinary.com" />
    
    <!-- DNS Prefetch -->
    <link rel="dns-prefetch" href="//api.razorpay.com" />
    <link rel="dns-prefetch" href="//js.stripe.com" />
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Worship E-Commerce",
      "description": "Sacred products and spiritual services platform",
      "url": "https://worship-ecommerce.vercel.app/",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://worship-ecommerce.vercel.app/products?search={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "sameAs": [
        "https://facebook.com/worshipecommerce",
        "https://twitter.com/worshipecommerce",
        "https://instagram.com/worshipecommerce"
      ]
    }
    </script>
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    
    <!-- Performance Hints -->
    <link rel="preload" href="/fonts/poppins-400.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/fonts/lora-400.woff2" as="font" type="font/woff2" crossorigin />
    
    <!-- Critical CSS will be inlined here by build process -->
    <style>
      /* Critical CSS for above-the-fold content */
      body {
        font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #fafaf9;
        color: #1c1917;
      }
      
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #d97706, #c9a227);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .om-symbol {
        font-size: 2rem;
        color: white;
        margin-bottom: 1rem;
        animation: pulse 2s infinite;
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
      <div style="text-align: center;">
        <div class="om-symbol">🕉️</div>
        <div class="loading-spinner"></div>
        <p style="color: white; margin-top: 1rem; font-weight: 500;">Loading Sacred Experience...</p>
      </div>
    </div>
    
    <!-- Main App Container -->
    <div id="root"></div>
    
    <!-- No Script Fallback -->
    <noscript>
      <div style="text-align: center; padding: 2rem; background: #fef7ed; border: 2px solid #d97706; margin: 1rem; border-radius: 8px;">
        <h2 style="color: #d97706; margin-bottom: 1rem;">🕉️ Worship E-Commerce</h2>
        <p style="color: #92400e; margin-bottom: 1rem;">
          JavaScript is required to use this application. Please enable JavaScript in your browser settings.
        </p>
        <p style="color: #78716c; font-size: 0.9rem;">
          For the best spiritual shopping experience, we recommend using a modern browser with JavaScript enabled.
        </p>
      </div>
    </noscript>
    
    <!-- Service Worker Registration -->
    <script>
      // Remove loading screen when page loads
      window.addEventListener('load', function() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          setTimeout(() => {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => {
              loadingScreen.remove();
            }, 500);
          }, 1000);
        }
      });
      
      // Register service worker for PWA functionality
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
    
    <!-- Module Script -->
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
