const jwt = require('jsonwebtoken');
const config = require('../config/env');
const logger = require('../config/logger');

/**
 * Generate JWT access token
 * @param {Object} payload - Token payload
 * @returns {String} JWT token
 */
const generateAccessToken = (payload) => {
  try {
    return jwt.sign(payload, config.JWT_SECRET, {
      expiresIn: config.JWT_EXPIRE,
      issuer: 'worship-ecommerce',
      audience: 'worship-ecommerce-users'
    });
  } catch (error) {
    logger.errorWithContext(error, { payload }, 'Error generating access token');
    throw new Error('Token generation failed');
  }
};

/**
 * Generate JWT refresh token
 * @param {Object} payload - Token payload
 * @returns {String} JWT refresh token
 */
const generateRefreshToken = (payload) => {
  try {
    return jwt.sign(payload, config.JWT_REFRESH_SECRET, {
      expiresIn: config.JWT_REFRESH_EXPIRE,
      issuer: 'worship-ecommerce',
      audience: 'worship-ecommerce-users'
    });
  } catch (error) {
    logger.errorWithContext(error, { payload }, 'Error generating refresh token');
    throw new Error('Refresh token generation failed');
  }
};

/**
 * Generate both access and refresh tokens
 * @param {Object} user - User object
 * @returns {Object} Token pair
 */
const generateTokenPair = (user) => {
  const payload = {
    id: user._id,
    email: user.email,
    role: user.role,
    isEmailVerified: user.isEmailVerified
  };

  const accessToken = generateAccessToken(payload);
  const refreshToken = generateRefreshToken(payload);

  return {
    accessToken,
    refreshToken,
    expiresIn: config.JWT_EXPIRE
  };
};

/**
 * Verify JWT access token
 * @param {String} token - JWT token
 * @returns {Object} Decoded payload
 */
const verifyAccessToken = (token) => {
  try {
    return jwt.verify(token, config.JWT_SECRET, {
      issuer: 'worship-ecommerce',
      audience: 'worship-ecommerce-users'
    });
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Access token expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid access token');
    } else {
      logger.errorWithContext(error, { token: token?.substring(0, 20) + '...' }, 'Error verifying access token');
      throw new Error('Token verification failed');
    }
  }
};

/**
 * Verify JWT refresh token
 * @param {String} token - JWT refresh token
 * @returns {Object} Decoded payload
 */
const verifyRefreshToken = (token) => {
  try {
    return jwt.verify(token, config.JWT_REFRESH_SECRET, {
      issuer: 'worship-ecommerce',
      audience: 'worship-ecommerce-users'
    });
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Refresh token expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid refresh token');
    } else {
      logger.errorWithContext(error, { token: token?.substring(0, 20) + '...' }, 'Error verifying refresh token');
      throw new Error('Refresh token verification failed');
    }
  }
};

/**
 * Extract token from Authorization header
 * @param {String} authHeader - Authorization header value
 * @returns {String|null} JWT token
 */
const extractTokenFromHeader = (authHeader) => {
  if (!authHeader) return null;
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
};

/**
 * Generate email verification token
 * @param {String} email - User email
 * @returns {String} Verification token
 */
const generateEmailVerificationToken = (email) => {
  try {
    return jwt.sign(
      { email, type: 'email_verification' },
      config.JWT_SECRET,
      { expiresIn: '24h' }
    );
  } catch (error) {
    logger.errorWithContext(error, { email }, 'Error generating email verification token');
    throw new Error('Email verification token generation failed');
  }
};

/**
 * Verify email verification token
 * @param {String} token - Verification token
 * @returns {Object} Decoded payload
 */
const verifyEmailVerificationToken = (token) => {
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET);
    if (decoded.type !== 'email_verification') {
      throw new Error('Invalid token type');
    }
    return decoded;
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Email verification token expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid email verification token');
    } else {
      logger.errorWithContext(error, { token: token?.substring(0, 20) + '...' }, 'Error verifying email verification token');
      throw new Error('Email verification token verification failed');
    }
  }
};

/**
 * Generate password reset token
 * @param {String} userId - User ID
 * @param {String} email - User email
 * @returns {String} Reset token
 */
const generatePasswordResetToken = (userId, email) => {
  try {
    return jwt.sign(
      { 
        userId, 
        email, 
        type: 'password_reset',
        timestamp: Date.now()
      },
      config.JWT_SECRET,
      { expiresIn: '1h' }
    );
  } catch (error) {
    logger.errorWithContext(error, { userId, email }, 'Error generating password reset token');
    throw new Error('Password reset token generation failed');
  }
};

/**
 * Verify password reset token
 * @param {String} token - Reset token
 * @returns {Object} Decoded payload
 */
const verifyPasswordResetToken = (token) => {
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET);
    if (decoded.type !== 'password_reset') {
      throw new Error('Invalid token type');
    }
    return decoded;
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Password reset token expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid password reset token');
    } else {
      logger.errorWithContext(error, { token: token?.substring(0, 20) + '...' }, 'Error verifying password reset token');
      throw new Error('Password reset token verification failed');
    }
  }
};

/**
 * Decode token without verification (for debugging)
 * @param {String} token - JWT token
 * @returns {Object} Decoded payload
 */
const decodeToken = (token) => {
  try {
    return jwt.decode(token);
  } catch (error) {
    logger.errorWithContext(error, { token: token?.substring(0, 20) + '...' }, 'Error decoding token');
    return null;
  }
};

/**
 * Check if token is expired
 * @param {String} token - JWT token
 * @returns {Boolean} Is expired
 */
const isTokenExpired = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return true;
    
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
};

/**
 * Get token expiration time
 * @param {String} token - JWT token
 * @returns {Date|null} Expiration date
 */
const getTokenExpiration = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return null;
    
    return new Date(decoded.exp * 1000);
  } catch (error) {
    return null;
  }
};

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  generateTokenPair,
  verifyAccessToken,
  verifyRefreshToken,
  extractTokenFromHeader,
  generateEmailVerificationToken,
  verifyEmailVerificationToken,
  generatePasswordResetToken,
  verifyPasswordResetToken,
  decodeToken,
  isTokenExpired,
  getTokenExpiration
};
