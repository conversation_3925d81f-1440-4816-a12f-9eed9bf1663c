const winston = require('winston');
const path = require('path');
const config = require('./env');

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Define console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, stack }) => {
    return `${timestamp} [${level}]: ${stack || message}`;
  })
);

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.dirname(config.LOG_FILE);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create logger instance
const logger = winston.createLogger({
  level: config.LOG_LEVEL,
  format: logFormat,
  defaultMeta: { service: 'worship-ecommerce-backend' },
  transports: [
    // Write all logs with level 'error' and below to error.log
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // Write all logs to combined.log
    new winston.transports.File({
      filename: config.LOG_FILE,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// Add console transport for development
if (config.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// Create a stream object for Morgan HTTP logging
logger.stream = {
  write: (message) => {
    logger.info(message.trim());
  }
};

// Helper functions for structured logging
const logWithContext = (level, message, context = {}) => {
  logger.log(level, message, {
    ...context,
    timestamp: new Date().toISOString(),
    environment: config.NODE_ENV
  });
};

// Enhanced logging methods
const enhancedLogger = {
  ...logger,
  
  // API request logging
  apiRequest: (req, message = 'API Request') => {
    logWithContext('info', message, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      requestId: req.id
    });
  },
  
  // API response logging
  apiResponse: (req, res, message = 'API Response') => {
    logWithContext('info', message, {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: res.get('X-Response-Time'),
      userId: req.user?.id,
      requestId: req.id
    });
  },
  
  // Database operation logging
  dbOperation: (operation, collection, query = {}, message = 'Database Operation') => {
    logWithContext('debug', message, {
      operation,
      collection,
      query: JSON.stringify(query)
    });
  },
  
  // Payment logging
  payment: (paymentData, message = 'Payment Operation') => {
    logWithContext('info', message, {
      paymentId: paymentData.id,
      amount: paymentData.amount,
      currency: paymentData.currency,
      status: paymentData.status,
      gateway: paymentData.gateway,
      userId: paymentData.userId
    });
  },
  
  // Security logging
  security: (event, details = {}, message = 'Security Event') => {
    logWithContext('warn', message, {
      event,
      ...details,
      severity: 'security'
    });
  },
  
  // Performance logging
  performance: (operation, duration, details = {}, message = 'Performance Metric') => {
    logWithContext('info', message, {
      operation,
      duration: `${duration}ms`,
      ...details,
      type: 'performance'
    });
  },
  
  // Business logic logging
  business: (event, details = {}, message = 'Business Event') => {
    logWithContext('info', message, {
      event,
      ...details,
      type: 'business'
    });
  },
  
  // Error with context
  errorWithContext: (error, context = {}, message = 'Application Error') => {
    logWithContext('error', message, {
      error: error.message,
      stack: error.stack,
      ...context
    });
  }
};

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  enhancedLogger.errorWithContext(error, {}, 'Uncaught Exception');
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  enhancedLogger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

module.exports = enhancedLogger;
