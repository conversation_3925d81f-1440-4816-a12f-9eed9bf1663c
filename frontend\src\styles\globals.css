@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Lora:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400;
  }
  
  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.neutral.300') theme('colors.neutral.100');
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-md hover:shadow-lg;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500 shadow-md hover:shadow-lg;
  }
  
  .btn-outline {
    @apply btn border border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-neutral-700 hover:bg-neutral-100 focus:ring-neutral-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-neutral-200 overflow-hidden;
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
  }
  
  /* Input styles */
  .input {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }
  
  .input-error {
    @apply input border-red-500 focus:ring-red-500;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-neutral-200 border-t-primary-500;
  }
  
  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent;
  }
  
  /* Spiritual decorative elements */
  .om-symbol::before {
    content: "🕉️";
    @apply mr-2;
  }
  
  .lotus-divider {
    @apply relative flex items-center justify-center my-8;
  }
  
  .lotus-divider::before,
  .lotus-divider::after {
    content: "";
    @apply flex-1 h-px bg-gradient-to-r from-transparent via-primary-300 to-transparent;
  }
  
  .lotus-divider span {
    @apply px-4 text-primary-500 text-lg;
  }
  
  /* Product card styles */
  .product-card {
    @apply card-hover group cursor-pointer;
  }
  
  .product-card-image {
    @apply aspect-square w-full object-cover transition-transform duration-300 group-hover:scale-105;
  }
  
  .product-card-content {
    @apply p-4;
  }
  
  .product-card-title {
    @apply font-medium text-neutral-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors duration-200;
  }
  
  .product-card-price {
    @apply text-lg font-semibold text-primary-600;
  }
  
  .product-card-original-price {
    @apply text-sm text-neutral-500 line-through ml-2;
  }
  
  /* Festival banner styles */
  .festival-banner {
    @apply relative bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-16 px-8 rounded-2xl overflow-hidden;
  }
  
  .festival-banner::before {
    content: "";
    @apply absolute inset-0 bg-spiritual-pattern opacity-10;
  }
  
  /* Responsive utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Animation utilities */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  .animate-stagger > * {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  .animate-stagger > *:nth-child(1) { animation-delay: 0.1s; }
  .animate-stagger > *:nth-child(2) { animation-delay: 0.2s; }
  .animate-stagger > *:nth-child(3) { animation-delay: 0.3s; }
  .animate-stagger > *:nth-child(4) { animation-delay: 0.4s; }
  .animate-stagger > *:nth-child(5) { animation-delay: 0.5s; }
  .animate-stagger > *:nth-child(6) { animation-delay: 0.6s; }
  
  /* Text utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .bg-glass {
    @apply bg-white/80 backdrop-blur-sm;
  }
  
  .bg-glass-dark {
    @apply bg-neutral-900/80 backdrop-blur-sm;
  }
  
  .border-gradient {
    border-image: linear-gradient(45deg, theme('colors.primary.500'), theme('colors.secondary.500')) 1;
  }
  
  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-only {
      display: block !important;
    }
  }
  
  /* Dark mode utilities (for future implementation) */
  @media (prefers-color-scheme: dark) {
    .dark-mode-auto {
      @apply bg-neutral-900 text-neutral-100;
    }
  }
  
  /* High contrast mode */
  @media (prefers-contrast: high) {
    .high-contrast {
      @apply border-2 border-black;
    }
  }
  
  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .motion-reduce {
      animation: none !important;
      transition: none !important;
    }
  }
}
