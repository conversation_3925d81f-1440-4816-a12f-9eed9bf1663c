const User = require('../models/User');
const config = require('../config/env');
const logger = require('../config/logger');
const { asyncHandler, ValidationError, AuthenticationError } = require('../middlewares/errorHandler');
const { generateTokenPair, verifyRefreshToken } = require('../utils/jwt');
const { validatePasswordStrength } = require('../utils/hashPassword');

/**
 * Register a new user
 */
const register = asyncHandler(async (req, res) => {
  const { firstName, lastName, email, password, phone, acceptTerms } = req.body;

  // Validate required fields
  if (!firstName || !lastName || !email || !password || !phone) {
    throw new ValidationError('All fields are required');
  }

  if (!acceptTerms) {
    throw new ValidationError('You must accept the terms and conditions');
  }

  // Validate password strength
  const passwordValidation = validatePasswordStrength(password);
  if (!passwordValidation.isValid) {
    throw new ValidationError('Password does not meet requirements', {
      password: passwordValidation.errors
    });
  }

  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [{ email }, { phone }]
  });

  if (existingUser) {
    if (existingUser.email === email) {
      throw new ValidationError('User with this email already exists');
    }
    if (existingUser.phone === phone) {
      throw new ValidationError('User with this phone number already exists');
    }
  }

  // Create new user
  const user = new User({
    firstName,
    lastName,
    email,
    password,
    phone
  });

  await user.save();

  // Generate tokens
  const tokens = generateTokenPair(user);

  // Save refresh token
  user.refreshTokens.push({ token: tokens.refreshToken });
  await user.save();

  // Set cookies
  res.cookie('accessToken', tokens.accessToken, {
    httpOnly: true,
    secure: config.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 15 * 60 * 1000 // 15 minutes
  });

  res.cookie('refreshToken', tokens.refreshToken, {
    httpOnly: true,
    secure: config.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  });

  // Remove password from response
  const userResponse = user.toObject();
  delete userResponse.password;
  delete userResponse.refreshTokens;

  logger.business('user_registered', {
    userId: user._id,
    email: user.email,
    role: user.role
  }, 'New user registered');

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user: userResponse,
      tokens
    }
  });
});

/**
 * Login user
 */
const login = asyncHandler(async (req, res) => {
  const { email, password, rememberMe } = req.body;

  // Validate required fields
  if (!email || !password) {
    throw new ValidationError('Email and password are required');
  }

  // Find user and include password
  const user = await User.findByCredentials(email, password);

  // Generate tokens
  const tokens = generateTokenPair(user);

  // Save refresh token
  user.refreshTokens.push({ token: tokens.refreshToken });
  await user.save();

  // Set cookie expiration based on rememberMe
  const accessTokenExpiry = rememberMe ? 24 * 60 * 60 * 1000 : 15 * 60 * 1000; // 24 hours or 15 minutes
  const refreshTokenExpiry = rememberMe ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000; // 30 days or 7 days

  // Set cookies
  res.cookie('accessToken', tokens.accessToken, {
    httpOnly: true,
    secure: config.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: accessTokenExpiry
  });

  res.cookie('refreshToken', tokens.refreshToken, {
    httpOnly: true,
    secure: config.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: refreshTokenExpiry
  });

  // Remove sensitive data from response
  const userResponse = user.toObject();
  delete userResponse.password;
  delete userResponse.refreshTokens;

  logger.business('user_login', {
    userId: user._id,
    email: user.email,
    role: user.role,
    rememberMe
  }, 'User logged in');

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: userResponse,
      tokens
    }
  });
});

/**
 * Refresh access token
 */
const refreshToken = asyncHandler(async (req, res) => {
  const { refreshToken: token } = req.cookies;

  if (!token) {
    throw new AuthenticationError('Refresh token not provided');
  }

  // Verify refresh token
  const decoded = verifyRefreshToken(token);

  // Find user and check if refresh token exists
  const user = await User.findById(decoded.id);
  if (!user) {
    throw new AuthenticationError('Invalid refresh token');
  }

  const tokenExists = user.refreshTokens.some(t => t.token === token);
  if (!tokenExists) {
    throw new AuthenticationError('Invalid refresh token');
  }

  // Generate new tokens
  const tokens = generateTokenPair(user);

  // Replace old refresh token with new one
  user.refreshTokens = user.refreshTokens.filter(t => t.token !== token);
  user.refreshTokens.push({ token: tokens.refreshToken });
  await user.save();

  // Set new cookies
  res.cookie('accessToken', tokens.accessToken, {
    httpOnly: true,
    secure: config.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 15 * 60 * 1000 // 15 minutes
  });

  res.cookie('refreshToken', tokens.refreshToken, {
    httpOnly: true,
    secure: config.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  });

  logger.business('token_refreshed', {
    userId: user._id,
    email: user.email
  }, 'Access token refreshed');

  res.json({
    success: true,
    message: 'Token refreshed successfully',
    data: { tokens }
  });
});

/**
 * Logout user
 */
const logout = asyncHandler(async (req, res) => {
  const { refreshToken: token } = req.cookies;

  if (token && req.user) {
    // Remove refresh token from database
    await User.findByIdAndUpdate(req.user.id, {
      $pull: { refreshTokens: { token } }
    });
  }

  // Clear cookies
  res.clearCookie('accessToken');
  res.clearCookie('refreshToken');

  logger.business('user_logout', {
    userId: req.user?.id,
    email: req.user?.email
  }, 'User logged out');

  res.json({
    success: true,
    message: 'Logout successful'
  });
});

/**
 * Get current user profile
 */
const getProfile = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id)
    .populate('wishlist', 'name price images')
    .select('-password -refreshTokens');

  res.json({
    success: true,
    data: { user }
  });
});

/**
 * Update user profile
 */
const updateProfile = asyncHandler(async (req, res) => {
  const allowedUpdates = ['firstName', 'lastName', 'phone', 'dateOfBirth', 'gender', 'avatar', 'preferences'];
  const updates = {};

  // Filter allowed updates
  Object.keys(req.body).forEach(key => {
    if (allowedUpdates.includes(key)) {
      updates[key] = req.body[key];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.user.id,
    updates,
    { new: true, runValidators: true }
  ).select('-password -refreshTokens');

  logger.business('profile_updated', {
    userId: user._id,
    updatedFields: Object.keys(updates)
  }, 'User profile updated');

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: { user }
  });
});

/**
 * Change password
 */
const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    throw new ValidationError('Current password and new password are required');
  }

  // Get user with password
  const user = await User.findById(req.user.id).select('+password');

  // Verify current password
  const isCurrentPasswordValid = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    throw new ValidationError('Current password is incorrect');
  }

  // Validate new password strength
  const passwordValidation = validatePasswordStrength(newPassword);
  if (!passwordValidation.isValid) {
    throw new ValidationError('New password does not meet requirements', {
      password: passwordValidation.errors
    });
  }

  // Update password
  user.password = newPassword;
  await user.save();

  // Invalidate all refresh tokens (force re-login on all devices)
  user.refreshTokens = [];
  await user.save();

  logger.security('password_changed', {
    userId: user._id,
    email: user.email
  }, 'User password changed');

  res.json({
    success: true,
    message: 'Password changed successfully. Please login again.'
  });
});

/**
 * Add address
 */
const addAddress = asyncHandler(async (req, res) => {
  const { street, city, state, postalCode, country, landmark, isDefault } = req.body;

  const user = await User.findById(req.user.id);

  // If this is set as default, unset other default addresses
  if (isDefault) {
    user.addresses.forEach(addr => addr.isDefault = false);
  }

  user.addresses.push({
    street,
    city,
    state,
    postalCode,
    country: country || 'India',
    landmark,
    isDefault: isDefault || user.addresses.length === 0 // First address is default
  });

  await user.save();

  res.json({
    success: true,
    message: 'Address added successfully',
    data: { addresses: user.addresses }
  });
});

/**
 * Update address
 */
const updateAddress = asyncHandler(async (req, res) => {
  const { addressId } = req.params;
  const updates = req.body;

  const user = await User.findById(req.user.id);
  const address = user.addresses.id(addressId);

  if (!address) {
    throw new ValidationError('Address not found');
  }

  // If setting as default, unset other default addresses
  if (updates.isDefault) {
    user.addresses.forEach(addr => addr.isDefault = false);
  }

  Object.keys(updates).forEach(key => {
    address[key] = updates[key];
  });

  await user.save();

  res.json({
    success: true,
    message: 'Address updated successfully',
    data: { addresses: user.addresses }
  });
});

/**
 * Delete address
 */
const deleteAddress = asyncHandler(async (req, res) => {
  const { addressId } = req.params;

  const user = await User.findById(req.user.id);
  user.addresses.pull(addressId);
  await user.save();

  res.json({
    success: true,
    message: 'Address deleted successfully',
    data: { addresses: user.addresses }
  });
});

module.exports = {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  changePassword,
  addAddress,
  updateAddress,
  deleteAddress
};
