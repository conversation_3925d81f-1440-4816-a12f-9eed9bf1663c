import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { authApi } from '../api/authApi'
import toast from 'react-hot-toast'

// Initial state
const initialState = {
  user: null,
  isLoading: true,
  isAuthenticated: false,
  error: null
}

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT: 'LOGOUT',
  UPDATE_USER: 'UPDATE_USER',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR'
}

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      }
    
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null
      }
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      }
    
    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      }
    
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false
      }
    
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      }
    
    default:
      return state
  }
}

// Create context
const AuthContext = createContext()

// Provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Check if user is authenticated on app load
  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true })
      
      const response = await authApi.getProfile()
      
      if (response.success) {
        dispatch({ 
          type: AUTH_ACTIONS.LOGIN_SUCCESS, 
          payload: response.data.user 
        })
      } else {
        dispatch({ type: AUTH_ACTIONS.LOGOUT })
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      dispatch({ type: AUTH_ACTIONS.LOGOUT })
    } finally {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false })
    }
  }

  const login = async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true })
      dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR })
      
      const response = await authApi.login(credentials)
      
      if (response.success) {
        dispatch({ 
          type: AUTH_ACTIONS.LOGIN_SUCCESS, 
          payload: response.data.user 
        })
        
        toast.success('Welcome back! 🙏')
        return { success: true }
      } else {
        const errorMessage = response.message || 'Login failed'
        dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage })
        toast.error(errorMessage)
        return { success: false, error: errorMessage }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Login failed. Please try again.'
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage })
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  const register = async (userData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true })
      dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR })
      
      const response = await authApi.register(userData)
      
      if (response.success) {
        dispatch({ 
          type: AUTH_ACTIONS.LOGIN_SUCCESS, 
          payload: response.data.user 
        })
        
        toast.success('Welcome to our spiritual community! 🕉️')
        return { success: true }
      } else {
        const errorMessage = response.message || 'Registration failed'
        dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage })
        toast.error(errorMessage)
        return { success: false, error: errorMessage }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Registration failed. Please try again.'
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage })
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      dispatch({ type: AUTH_ACTIONS.LOGOUT })
      toast.success('Logged out successfully')
    }
  }

  const updateProfile = async (profileData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR })
      
      const response = await authApi.updateProfile(profileData)
      
      if (response.success) {
        dispatch({ 
          type: AUTH_ACTIONS.UPDATE_USER, 
          payload: response.data.user 
        })
        
        toast.success('Profile updated successfully')
        return { success: true }
      } else {
        const errorMessage = response.message || 'Profile update failed'
        dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage })
        toast.error(errorMessage)
        return { success: false, error: errorMessage }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Profile update failed. Please try again.'
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage })
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  const changePassword = async (passwordData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR })
      
      const response = await authApi.changePassword(passwordData)
      
      if (response.success) {
        toast.success('Password changed successfully. Please login again.')
        // Force logout after password change
        dispatch({ type: AUTH_ACTIONS.LOGOUT })
        return { success: true }
      } else {
        const errorMessage = response.message || 'Password change failed'
        dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage })
        toast.error(errorMessage)
        return { success: false, error: errorMessage }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Password change failed. Please try again.'
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage })
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  const addAddress = async (addressData) => {
    try {
      const response = await authApi.addAddress(addressData)
      
      if (response.success) {
        dispatch({ 
          type: AUTH_ACTIONS.UPDATE_USER, 
          payload: { addresses: response.data.addresses }
        })
        
        toast.success('Address added successfully')
        return { success: true }
      } else {
        toast.error(response.message || 'Failed to add address')
        return { success: false, error: response.message }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to add address'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  const updateAddress = async (addressId, addressData) => {
    try {
      const response = await authApi.updateAddress(addressId, addressData)
      
      if (response.success) {
        dispatch({ 
          type: AUTH_ACTIONS.UPDATE_USER, 
          payload: { addresses: response.data.addresses }
        })
        
        toast.success('Address updated successfully')
        return { success: true }
      } else {
        toast.error(response.message || 'Failed to update address')
        return { success: false, error: response.message }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to update address'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  const deleteAddress = async (addressId) => {
    try {
      const response = await authApi.deleteAddress(addressId)
      
      if (response.success) {
        dispatch({ 
          type: AUTH_ACTIONS.UPDATE_USER, 
          payload: { addresses: response.data.addresses }
        })
        
        toast.success('Address deleted successfully')
        return { success: true }
      } else {
        toast.error(response.message || 'Failed to delete address')
        return { success: false, error: response.message }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to delete address'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR })
  }

  const value = {
    ...state,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    addAddress,
    updateAddress,
    deleteAddress,
    clearError,
    checkAuth
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export const useAuthContext = () => {
  const context = useContext(AuthContext)
  
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider')
  }
  
  return context
}

export default AuthContext
