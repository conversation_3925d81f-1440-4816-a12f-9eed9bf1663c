{"name": "worship-ecommerce-frontend", "version": "1.0.0", "description": "Frontend for Worship E-Commerce Platform", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext js,jsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "@tanstack/react-query": "^5.8.4", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "react-intersection-observer": "^9.5.3", "framer-motion": "^10.16.5", "react-helmet-async": "^1.3.0", "react-lazyload": "^3.2.0", "swiper": "^11.0.5", "embla-carousel-react": "^8.0.0-rc19", "react-calendar": "^4.6.0", "recharts": "^2.8.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/aspect-ratio": "^0.4.2"}, "engines": {"node": ">=18.0.0"}}