# 🕉️ Worship E-Commerce Website

A professional, fully functional worship e-commerce platform built with the MERN stack, featuring spiritual products, puja booking services, and donation management.

## ✨ Features

### 🛍️ E-Commerce Core
- **Product Catalog**: Idols, incense, lamps, pooja kits, books, temple wear, decor, digital wallpapers, mantra audios
- **Smart Search & Filters**: Category, price, deity, rating, festival-based filtering
- **Shopping Cart & Wishlist**: Seamless shopping experience
- **Multi-Payment Gateway**: Razorpay (India) + Stripe (Global)
- **Order Tracking**: Real-time order status updates

### 🙏 Spiritual Services
- **Puja Booking**: Calendar-based slot booking with add-ons (prasadam, livestream)
- **Donations**: Multiple causes, preset amounts (₹251/₹501/₹1100), recurring options
- **Festival Pages**: Dedicated landing pages for <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
- **AI Recommendations**: "Often bought for Lakshmi Puja", "Festival Kits"

### 👥 Multi-Role System
- **Users**: Profile management, order history, wishlist, puja bookings
- **Vendors**: Onboarding, product management, inventory, analytics dashboard
- **Admins**: Complete platform management, analytics, content moderation

### 🌐 Advanced Features
- **Multi-Language**: English + Hindi support
- **Responsive Design**: Mobile-first, spiritual-inspired UI
- **SEO Optimized**: Festival landing pages, meta tags, structured data
- **Accessibility**: WCAG compliant, ARIA labels
- **Performance**: Lighthouse score 90+ (mobile & desktop)

## 🛠️ Tech Stack

### Frontend
- **Framework**: React 18 + Vite
- **Language**: JavaScript (ES6+)
- **Styling**: TailwindCSS with custom spiritual theme
- **State Management**: React Context API + Zustand
- **API Calls**: Axios + TanStack Query (React Query)
- **Authentication**: JWT with httpOnly cookies

### Backend
- **Runtime**: Node.js (Latest LTS)
- **Framework**: Express.js
- **Database**: MongoDB + Mongoose ODM
- **Authentication**: JWT + bcrypt + Google OAuth
- **Payments**: Razorpay + Stripe
- **File Storage**: Cloudinary
- **Notifications**: Twilio/MSG91 (Email, SMS, WhatsApp)

### DevOps & Deployment
- **Frontend**: Vercel
- **Backend**: Render/Heroku/AWS
- **Database**: MongoDB Atlas
- **CDN**: Cloudinary for images
- **Monitoring**: Error tracking and analytics

## 📁 Project Structure

```
worship-ecommerce/
├── backend/                       # Node.js + Express + MongoDB
│   ├── src/
│   │   ├── config/                # Config & DB
│   │   │   ├── db.js
│   │   │   ├── env.js
│   │   │   └── logger.js
│   │   │
│   │   ├── models/                # MongoDB Schemas
│   │   │   ├── User.js
│   │   │   ├── Product.js
│   │   │   ├── Order.js
│   │   │   ├── Vendor.js
│   │   │   ├── Review.js
│   │   │   ├── Donation.js
│   │   │   ├── PujaService.js
│   │   │   └── Booking.js
│   │   │
│   │   ├── controllers/           # Business logic
│   │   │   ├── auth.controller.js
│   │   │   ├── product.controller.js
│   │   │   ├── order.controller.js
│   │   │   ├── vendor.controller.js
│   │   │   ├── donation.controller.js
│   │   │   └── puja.controller.js
│   │   │
│   │   ├── routes/                # Express routes
│   │   │   ├── auth.routes.js
│   │   │   ├── product.routes.js
│   │   │   ├── order.routes.js
│   │   │   ├── vendor.routes.js
│   │   │   ├── donation.routes.js
│   │   │   ├── puja.routes.js
│   │   │   └── admin.routes.js
│   │   │
│   │   ├── middlewares/           # Security & validation
│   │   │   ├── authMiddleware.js
│   │   │   ├── errorHandler.js
│   │   │   └── validate.js
│   │   │
│   │   ├── services/              # External integrations
│   │   │   ├── payment.service.js
│   │   │   ├── cloudinary.service.js
│   │   │   └── email.service.js
│   │   │
│   │   ├── utils/                 # Helpers
│   │   │   ├── jwt.js
│   │   │   ├── hashPassword.js
│   │   │   └── formatPrice.js
│   │   │
│   │   ├── app.js                 # Express config
│   │   └── server.js              # Server entry
│   │
│   └── package.json
│
├── frontend/                      # React + Tailwind
│   ├── src/
│   │   ├── api/                   # API calls (Axios)
│   │   │   ├── authApi.js
│   │   │   ├── productApi.js
│   │   │   ├── orderApi.js
│   │   │   └── donationApi.js
│   │   │
│   │   ├── assets/                # Images, icons, fonts
│   │   │
│   │   ├── components/            # Reusable UI
│   │   │   ├── Navbar.jsx
│   │   │   ├── Footer.jsx
│   │   │   ├── ProductCard.jsx
│   │   │   ├── CartItem.jsx
│   │   │   ├── Loader.jsx
│   │   │   └── Calendar.jsx
│   │   │
│   │   ├── pages/                 # Page components
│   │   │   ├── Home.jsx
│   │   │   ├── ProductList.jsx
│   │   │   ├── ProductDetail.jsx
│   │   │   ├── Cart.jsx
│   │   │   ├── Checkout.jsx
│   │   │   ├── Orders.jsx
│   │   │   ├── Donations.jsx
│   │   │   ├── PujaBooking.jsx
│   │   │   ├── Profile.jsx
│   │   │   └── AdminDashboard.jsx
│   │   │
│   │   ├── context/               # Global state (React Context)
│   │   │   ├── AuthContext.js
│   │   │   └── CartContext.js
│   │   │
│   │   ├── hooks/                 # Custom hooks
│   │   │   ├── useAuth.js
│   │   │   └── useCart.js
│   │   │
│   │   ├── styles/                # Tailwind styles
│   │   │   └── globals.css
│   │   │
│   │   ├── App.jsx
│   │   └── main.jsx
│   │
│   ├── tailwind.config.js
│   ├── vite.config.js
│   └── package.json
│
├── shared/                        # Shared types/utils
│   ├── types/                     # Common interfaces
│   └── utils/                     # Common helpers
│
├── .env                           # Environment variables
├── .gitignore
└── README.md
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 9+
- MongoDB (local or Atlas)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd worship-ecommerce
   ```

2. **Install backend dependencies**
   ```bash
   cd backend
   npm install
   ```

3. **Install frontend dependencies**
   ```bash
   cd ../frontend
   npm install
   ```

4. **Environment Setup**
   ```bash
   # Copy the .env file and update with your values
   cp .env.example .env
   ```

5. **Configure environment variables in `.env`**
   ```env
   # Database
   MONGODB_URI=mongodb://localhost:27017/worship-ecommerce
   
   # JWT Secrets
   JWT_SECRET=your-super-secret-jwt-key
   JWT_REFRESH_SECRET=your-refresh-secret-key
   
   # Payment Gateways
   RAZORPAY_KEY_ID=your-razorpay-key-id
   RAZORPAY_KEY_SECRET=your-razorpay-key-secret
   STRIPE_SECRET_KEY=your-stripe-secret-key
   
   # Cloudinary (for image uploads)
   CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
   CLOUDINARY_API_KEY=your-cloudinary-api-key
   CLOUDINARY_API_SECRET=your-cloudinary-api-secret
   
   # Email (Gmail SMTP)
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   ```

6. **Start the development servers**
   
   **Backend (Terminal 1):**
   ```bash
   cd backend
   npm run dev
   ```
   
   **Frontend (Terminal 2):**
   ```bash
   cd frontend
   npm run dev
   ```

7. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000
   - API Health: http://localhost:5000/api/health

### Available Scripts

**Backend:**
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run seed` - Seed database with sample data

**Frontend:**
- `npm run dev` - Start development server with Vite
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🎨 Design System

### Color Palette
- **Primary**: Saffron (#D97706) - Sacred and auspicious
- **Secondary**: Gold (#C9A227) - Divine and prosperity
- **Neutral**: White, Beige, Light Gray
- **Accent**: Deep Orange for CTAs

### Typography
- **UI Text**: Poppins (clean, modern)
- **Headings**: Lora (elegant, spiritual quotes)
- **Icons**: Lucide React + custom spiritual icons

### Components
- Spiritual-inspired design with clean, minimal aesthetics
- Rounded corners, subtle shadows, smooth animations
- Accessible color contrast ratios
- Mobile-first responsive design

---

**Built with 🕉️ for the spiritual community**
