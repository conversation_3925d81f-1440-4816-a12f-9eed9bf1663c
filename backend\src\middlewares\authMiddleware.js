const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config/env');
const logger = require('../config/logger');
const { verifyAccessToken, extractTokenFromHeader } = require('../utils/jwt');

/**
 * Middleware to authenticate user using JWT token
 */
const authenticate = async (req, res, next) => {
  try {
    // Extract token from Authorization header or cookies
    let token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token && req.cookies && req.cookies.accessToken) {
      token = req.cookies.accessToken;
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }

    // Verify token
    const decoded = verifyAccessToken(token);
    
    // Find user
    const user = await User.findById(decoded.id).select('-password -refreshTokens');
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token. User not found.'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated.'
      });
    }

    // Add user to request object
    req.user = user;
    req.token = token;

    logger.apiRequest(req, 'User authenticated');
    next();
  } catch (error) {
    logger.errorWithContext(error, { 
      url: req.originalUrl,
      method: req.method,
      ip: req.ip 
    }, 'Authentication failed');

    if (error.message === 'Access token expired') {
      return res.status(401).json({
        success: false,
        message: 'Token expired. Please refresh your token.',
        code: 'TOKEN_EXPIRED'
      });
    }

    return res.status(401).json({
      success: false,
      message: 'Invalid token.'
    });
  }
};

/**
 * Middleware to authorize user based on roles
 * @param {...String} roles - Allowed roles
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    if (!roles.includes(req.user.role)) {
      logger.security('unauthorized_access', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: roles,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip
      }, 'Unauthorized access attempt');

      return res.status(403).json({
        success: false,
        message: 'Access denied. Insufficient permissions.'
      });
    }

    next();
  };
};

/**
 * Middleware for optional authentication (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    let token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token && req.cookies && req.cookies.accessToken) {
      token = req.cookies.accessToken;
    }

    if (token) {
      try {
        const decoded = verifyAccessToken(token);
        const user = await User.findById(decoded.id).select('-password -refreshTokens');
        
        if (user && user.isActive) {
          req.user = user;
          req.token = token;
        }
      } catch (error) {
        // Ignore token errors for optional auth
        logger.debug('Optional auth token invalid', { error: error.message });
      }
    }

    next();
  } catch (error) {
    // Don't fail for optional auth
    next();
  }
};

/**
 * Middleware to check if user owns the resource
 * @param {String} paramName - Parameter name containing the user ID
 */
const checkOwnership = (paramName = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    const resourceUserId = req.params[paramName] || req.body[paramName];
    
    // Admin can access any resource
    if (req.user.role === 'admin') {
      return next();
    }

    // User can only access their own resources
    if (req.user.id !== resourceUserId) {
      logger.security('ownership_violation', {
        userId: req.user.id,
        resourceUserId,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip
      }, 'User attempted to access resource they don\'t own');

      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only access your own resources.'
      });
    }

    next();
  };
};

/**
 * Middleware to check if user is verified
 */
const requireVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required.'
    });
  }

  if (!req.user.isEmailVerified) {
    return res.status(403).json({
      success: false,
      message: 'Email verification required.',
      code: 'EMAIL_NOT_VERIFIED'
    });
  }

  next();
};

/**
 * Middleware to check if user is a vendor
 */
const requireVendor = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.'
      });
    }

    if (req.user.role !== 'vendor' && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Vendor access required.'
      });
    }

    // If user is a vendor, check if vendor profile exists and is approved
    if (req.user.role === 'vendor') {
      const Vendor = require('../models/Vendor');
      const vendor = await Vendor.findOne({ userId: req.user.id });
      
      if (!vendor) {
        return res.status(403).json({
          success: false,
          message: 'Vendor profile not found.',
          code: 'VENDOR_PROFILE_NOT_FOUND'
        });
      }

      if (vendor.status !== 'approved') {
        return res.status(403).json({
          success: false,
          message: 'Vendor account is not approved.',
          code: 'VENDOR_NOT_APPROVED'
        });
      }

      req.vendor = vendor;
    }

    next();
  } catch (error) {
    logger.errorWithContext(error, { userId: req.user?.id }, 'Error in vendor middleware');
    return res.status(500).json({
      success: false,
      message: 'Internal server error.'
    });
  }
};

/**
 * Middleware to rate limit based on user
 */
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();

  return (req, res, next) => {
    const userId = req.user?.id || req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    if (requests.has(userId)) {
      const userRequests = requests.get(userId).filter(time => time > windowStart);
      requests.set(userId, userRequests);
    }

    const userRequests = requests.get(userId) || [];
    
    if (userRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'Too many requests. Please try again later.',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    userRequests.push(now);
    requests.set(userId, userRequests);

    next();
  };
};

/**
 * Middleware to log user activity
 */
const logActivity = (action) => {
  return (req, res, next) => {
    if (req.user) {
      logger.business('user_activity', {
        userId: req.user.id,
        action,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }, `User activity: ${action}`);
    }
    next();
  };
};

module.exports = {
  authenticate,
  authorize,
  optionalAuth,
  checkOwnership,
  requireVerification,
  requireVendor,
  userRateLimit,
  logActivity
};
