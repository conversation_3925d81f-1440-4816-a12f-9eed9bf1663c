const app = require('./app');
const config = require('./config/env');
const logger = require('./config/logger');
const { connectDB, createIndexes } = require('./config/db');

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('UNCAUGHT EXCEPTION! 💥 Shutting down...');
  logger.errorWithContext(err, {}, 'Uncaught Exception');
  process.exit(1);
});

// Connect to database
connectDB()
  .then(async () => {
    // Create database indexes
    await createIndexes();
    
    // Start server
    const server = app.listen(config.PORT, () => {
      logger.info(`🚀 Server running on port ${config.PORT} in ${config.NODE_ENV} mode`);
      logger.info(`📱 Frontend URL: ${config.FRONTEND_URL}`);
      logger.info(`🔗 API Base URL: http://localhost:${config.PORT}/api`);
      logger.info(`💾 Database: ${config.MONGODB_URI.replace(/\/\/.*@/, '//***:***@')}`);
      
      if (config.NODE_ENV === 'development') {
        logger.info('🛠️  Development mode - detailed logging enabled');
        logger.info('📋 Health check: http://localhost:' + config.PORT + '/health');
        logger.info('🔍 API health: http://localhost:' + config.PORT + '/api/health');
      }
    });

    // Store server instance for graceful shutdown
    app.server = server;

    // Handle server errors
    server.on('error', (error) => {
      if (error.syscall !== 'listen') {
        throw error;
      }

      const bind = typeof config.PORT === 'string'
        ? 'Pipe ' + config.PORT
        : 'Port ' + config.PORT;

      switch (error.code) {
        case 'EACCES':
          logger.error(`${bind} requires elevated privileges`);
          process.exit(1);
          break;
        case 'EADDRINUSE':
          logger.error(`${bind} is already in use`);
          process.exit(1);
          break;
        default:
          throw error;
      }
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (err, promise) => {
      logger.error('UNHANDLED REJECTION! 💥 Shutting down...');
      logger.errorWithContext(err, { promise }, 'Unhandled Promise Rejection');
      
      server.close(() => {
        process.exit(1);
      });
    });

    // Handle SIGTERM signal (Heroku, Docker, etc.)
    process.on('SIGTERM', () => {
      logger.info('👋 SIGTERM RECEIVED. Shutting down gracefully');
      
      server.close(() => {
        logger.info('💥 Process terminated!');
      });
    });

    // Handle SIGINT signal (Ctrl+C)
    process.on('SIGINT', () => {
      logger.info('👋 SIGINT RECEIVED. Shutting down gracefully');
      
      server.close(() => {
        logger.info('💥 Process terminated!');
        process.exit(0);
      });
    });

    // Log memory usage periodically in development
    if (config.NODE_ENV === 'development') {
      setInterval(() => {
        const memUsage = process.memoryUsage();
        logger.debug('Memory usage:', {
          rss: `${Math.round(memUsage.rss / 1024 / 1024)} MB`,
          heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`,
          heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`,
          external: `${Math.round(memUsage.external / 1024 / 1024)} MB`
        });
      }, 30000); // Every 30 seconds
    }

  })
  .catch((error) => {
    logger.error('Failed to start server:', error);
    process.exit(1);
  });
