const { validationResult } = require('express-validator');
const { ValidationError } = require('./errorHandler');
const logger = require('../config/logger');

/**
 * Middleware to handle validation errors from express-validator
 */
const validate = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = {};
    
    // Group errors by field
    errors.array().forEach(error => {
      if (!errorMessages[error.path]) {
        errorMessages[error.path] = [];
      }
      errorMessages[error.path].push(error.msg);
    });

    // Log validation errors
    logger.debug('Validation failed', {
      url: req.originalUrl,
      method: req.method,
      errors: errorMessages,
      body: req.body,
      userId: req.user?.id
    });

    throw new ValidationError('Validation failed', errorMessages);
  }
  
  next();
};

/**
 * Custom validation for MongoDB ObjectId
 */
const isValidObjectId = (value) => {
  return /^[0-9a-fA-F]{24}$/.test(value);
};

/**
 * Custom validation for Indian phone number
 */
const isValidIndianPhone = (value) => {
  return /^[6-9]\d{9}$/.test(value);
};

/**
 * Custom validation for postal code
 */
const isValidPostalCode = (value) => {
  return /^\d{6}$/.test(value);
};

/**
 * Custom validation for price
 */
const isValidPrice = (value) => {
  const price = parseFloat(value);
  return !isNaN(price) && price >= 0 && price <= 10000000;
};

/**
 * Custom validation for rating
 */
const isValidRating = (value) => {
  const rating = parseFloat(value);
  return !isNaN(rating) && rating >= 1 && rating <= 5;
};

/**
 * Custom validation for quantity
 */
const isValidQuantity = (value) => {
  const quantity = parseInt(value);
  return !isNaN(quantity) && quantity >= 1 && quantity <= 100;
};

/**
 * Custom validation for date range
 */
const isValidDateRange = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  return start <= end;
};

/**
 * Custom validation for file upload
 */
const isValidFileType = (mimetype, allowedTypes = ['image/jpeg', 'image/png', 'image/webp']) => {
  return allowedTypes.includes(mimetype);
};

/**
 * Custom validation for file size
 */
const isValidFileSize = (size, maxSize = 5 * 1024 * 1024) => { // 5MB default
  return size <= maxSize;
};

/**
 * Sanitize input data
 */
const sanitizeInput = (data) => {
  if (typeof data === 'string') {
    return data.trim();
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        sanitized[key] = sanitizeInput(data[key]);
      }
    }
    return sanitized;
  }
  
  return data;
};

/**
 * Middleware to sanitize request body
 */
const sanitizeBody = (req, res, next) => {
  if (req.body) {
    req.body = sanitizeInput(req.body);
  }
  next();
};

/**
 * Validate pagination parameters
 */
const validatePagination = (req, res, next) => {
  const { page = 1, limit = 12, sortBy, sortOrder = 'desc' } = req.query;
  
  // Validate page
  const pageNum = parseInt(page);
  if (isNaN(pageNum) || pageNum < 1) {
    req.query.page = 1;
  } else {
    req.query.page = pageNum;
  }
  
  // Validate limit
  const limitNum = parseInt(limit);
  if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
    req.query.limit = 12;
  } else {
    req.query.limit = limitNum;
  }
  
  // Validate sort order
  if (!['asc', 'desc'].includes(sortOrder)) {
    req.query.sortOrder = 'desc';
  }
  
  // Validate sort by (if provided)
  if (sortBy) {
    const allowedSortFields = [
      'name', 'price', 'rating', 'createdAt', 'updatedAt',
      'purchases', 'views', 'discountPercentage'
    ];
    
    if (!allowedSortFields.includes(sortBy)) {
      delete req.query.sortBy;
    }
  }
  
  next();
};

/**
 * Validate search parameters
 */
const validateSearch = (req, res, next) => {
  const { q, category, deity, festival, minPrice, maxPrice, rating } = req.query;
  
  // Validate search query
  if (q && typeof q === 'string') {
    req.query.q = q.trim().substring(0, 100); // Limit search query length
  }
  
  // Validate category
  if (category) {
    const validCategories = [
      'idols', 'incense', 'lamps', 'pooja_kits', 'books',
      'temple_wear', 'decor', 'digital_wallpapers', 'mantra_audios'
    ];
    
    if (!validCategories.includes(category)) {
      delete req.query.category;
    }
  }
  
  // Validate deity
  if (deity) {
    const validDeities = [
      'ganesha', 'shiva', 'vishnu', 'lakshmi', 'saraswati',
      'durga', 'krishna', 'rama', 'hanuman', 'kali', 'other'
    ];
    
    if (!validDeities.includes(deity)) {
      delete req.query.deity;
    }
  }
  
  // Validate festival
  if (festival) {
    const validFestivals = [
      'diwali', 'navratri', 'ganesh_chaturthi', 'dussehra',
      'holi', 'karva_chauth', 'janmashtami', 'maha_shivratri', 'other'
    ];
    
    if (!validFestivals.includes(festival)) {
      delete req.query.festival;
    }
  }
  
  // Validate price range
  if (minPrice) {
    const min = parseFloat(minPrice);
    if (isNaN(min) || min < 0) {
      delete req.query.minPrice;
    } else {
      req.query.minPrice = min;
    }
  }
  
  if (maxPrice) {
    const max = parseFloat(maxPrice);
    if (isNaN(max) || max < 0) {
      delete req.query.maxPrice;
    } else {
      req.query.maxPrice = max;
    }
  }
  
  // Validate rating
  if (rating) {
    const ratingNum = parseFloat(rating);
    if (isNaN(ratingNum) || ratingNum < 1 || ratingNum > 5) {
      delete req.query.rating;
    } else {
      req.query.rating = ratingNum;
    }
  }
  
  next();
};

/**
 * Validate file upload
 */
const validateFileUpload = (allowedTypes = ['image/jpeg', 'image/png', 'image/webp'], maxSize = 5 * 1024 * 1024) => {
  return (req, res, next) => {
    if (!req.file && !req.files) {
      return next();
    }
    
    const files = req.files || [req.file];
    const errors = [];
    
    files.forEach((file, index) => {
      if (!isValidFileType(file.mimetype, allowedTypes)) {
        errors.push(`File ${index + 1}: Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
      }
      
      if (!isValidFileSize(file.size, maxSize)) {
        errors.push(`File ${index + 1}: File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`);
      }
    });
    
    if (errors.length > 0) {
      throw new ValidationError('File validation failed', { files: errors });
    }
    
    next();
  };
};

module.exports = {
  validate,
  sanitizeBody,
  validatePagination,
  validateSearch,
  validateFileUpload,
  isValidObjectId,
  isValidIndianPhone,
  isValidPostalCode,
  isValidPrice,
  isValidRating,
  isValidQuantity,
  isValidDateRange,
  isValidFileType,
  isValidFileSize,
  sanitizeInput
};
