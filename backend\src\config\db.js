const mongoose = require('mongoose');
const config = require('./env');
const logger = require('./logger');

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(config.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      bufferMaxEntries: 0, // Disable mongoose buffering
      bufferCommands: false, // Disable mongoose buffering
    });

    logger.info(`MongoDB Connected: ${conn.connection.host}`);

    // Handle connection events
    mongoose.connection.on('connected', () => {
      logger.info('Mongoose connected to MongoDB');
    });

    mongoose.connection.on('error', (err) => {
      logger.error('Mongoose connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('Mongoose disconnected from MongoDB');
    });

    // Handle application termination
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('Mongoose connection closed through app termination');
        process.exit(0);
      } catch (error) {
        logger.error('Error closing mongoose connection:', error);
        process.exit(1);
      }
    });

  } catch (error) {
    logger.error('Database connection failed:', error);
    process.exit(1);
  }
};

// Database health check
const checkDBHealth = async () => {
  try {
    const state = mongoose.connection.readyState;
    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };
    
    return {
      status: states[state] || 'unknown',
      connected: state === 1,
      host: mongoose.connection.host,
      name: mongoose.connection.name
    };
  } catch (error) {
    return {
      status: 'error',
      connected: false,
      error: error.message
    };
  }
};

// Database statistics
const getDBStats = async () => {
  try {
    if (mongoose.connection.readyState !== 1) {
      throw new Error('Database not connected');
    }

    const stats = await mongoose.connection.db.stats();
    return {
      collections: stats.collections,
      dataSize: stats.dataSize,
      storageSize: stats.storageSize,
      indexes: stats.indexes,
      indexSize: stats.indexSize,
      objects: stats.objects
    };
  } catch (error) {
    logger.error('Error getting database stats:', error);
    throw error;
  }
};

// Create database indexes for better performance
const createIndexes = async () => {
  try {
    logger.info('Creating database indexes...');
    
    // User indexes
    await mongoose.connection.collection('users').createIndex({ email: 1 }, { unique: true });
    await mongoose.connection.collection('users').createIndex({ phone: 1 });
    await mongoose.connection.collection('users').createIndex({ role: 1 });
    
    // Product indexes
    await mongoose.connection.collection('products').createIndex({ name: 'text', description: 'text' });
    await mongoose.connection.collection('products').createIndex({ category: 1 });
    await mongoose.connection.collection('products').createIndex({ deity: 1 });
    await mongoose.connection.collection('products').createIndex({ festival: 1 });
    await mongoose.connection.collection('products').createIndex({ price: 1 });
    await mongoose.connection.collection('products').createIndex({ rating: -1 });
    await mongoose.connection.collection('products').createIndex({ isActive: 1 });
    await mongoose.connection.collection('products').createIndex({ vendorId: 1 });
    
    // Order indexes
    await mongoose.connection.collection('orders').createIndex({ userId: 1 });
    await mongoose.connection.collection('orders').createIndex({ status: 1 });
    await mongoose.connection.collection('orders').createIndex({ createdAt: -1 });
    await mongoose.connection.collection('orders').createIndex({ 'items.productId': 1 });
    
    // Review indexes
    await mongoose.connection.collection('reviews').createIndex({ productId: 1 });
    await mongoose.connection.collection('reviews').createIndex({ userId: 1 });
    await mongoose.connection.collection('reviews').createIndex({ rating: -1 });
    
    // Donation indexes
    await mongoose.connection.collection('donations').createIndex({ userId: 1 });
    await mongoose.connection.collection('donations').createIndex({ cause: 1 });
    await mongoose.connection.collection('donations').createIndex({ createdAt: -1 });
    
    // Puja Service indexes
    await mongoose.connection.collection('pujaservices').createIndex({ name: 'text', description: 'text' });
    await mongoose.connection.collection('pujaservices').createIndex({ deity: 1 });
    await mongoose.connection.collection('pujaservices').createIndex({ price: 1 });
    
    // Booking indexes
    await mongoose.connection.collection('bookings').createIndex({ userId: 1 });
    await mongoose.connection.collection('bookings').createIndex({ pujaServiceId: 1 });
    await mongoose.connection.collection('bookings').createIndex({ date: 1, timeSlot: 1 });
    await mongoose.connection.collection('bookings').createIndex({ status: 1 });
    
    logger.info('Database indexes created successfully');
  } catch (error) {
    logger.error('Error creating database indexes:', error);
  }
};

module.exports = {
  connectDB,
  checkDBHealth,
  getDBStats,
  createIndexes
};
