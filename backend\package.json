{"name": "@worship-ecommerce/backend", "version": "1.0.0", "description": "Backend API for Worship E-Commerce", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc && tsc-alias", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "seed": "ts-node src/scripts/seed.ts"}, "dependencies": {"@worship-ecommerce/shared": "*", "express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "razorpay": "^2.9.2", "stripe": "^14.9.0", "dotenv": "^16.3.1", "cookie-parser": "^1.4.6", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "joi": "^17.11.0", "moment": "^2.29.4", "node-cron": "^3.0.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/cookie-parser": "^1.4.6", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/joi": "^17.2.3", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-jwt": "^3.0.13", "typescript": "^5.3.0", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "tsc-alias": "^1.8.8", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "engines": {"node": ">=18.0.0"}}