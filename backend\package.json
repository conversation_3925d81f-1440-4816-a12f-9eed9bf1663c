{"name": "worship-ecommerce-backend", "version": "1.0.0", "description": "Backend API for Worship E-Commerce Platform", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "test": "jest", "seed": "node src/scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "razorpay": "^2.9.2", "stripe": "^14.9.0", "dotenv": "^16.3.1", "cookie-parser": "^1.4.6", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "joi": "^17.11.0", "moment": "^2.29.4", "node-cron": "^3.0.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}