const bcrypt = require('bcryptjs');
const config = require('../config/env');
const logger = require('../config/logger');

/**
 * Hash a password using bcrypt
 * @param {String} password - Plain text password
 * @returns {Promise<String>} Hashed password
 */
const hashPassword = async (password) => {
  try {
    if (!password) {
      throw new Error('Password is required');
    }

    if (typeof password !== 'string') {
      throw new Error('Password must be a string');
    }

    if (password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }

    const salt = await bcrypt.genSalt(config.BCRYPT_SALT_ROUNDS);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    logger.debug('Password hashed successfully');
    return hashedPassword;
  } catch (error) {
    logger.errorWithContext(error, {}, 'Error hashing password');
    throw error;
  }
};

/**
 * Compare a plain text password with a hashed password
 * @param {String} password - Plain text password
 * @param {String} hashedPassword - Hashed password
 * @returns {Promise<Boolean>} Match result
 */
const comparePassword = async (password, hashedPassword) => {
  try {
    if (!password || !hashedPassword) {
      throw new Error('Password and hashed password are required');
    }

    if (typeof password !== 'string' || typeof hashedPassword !== 'string') {
      throw new Error('Password and hashed password must be strings');
    }

    const isMatch = await bcrypt.compare(password, hashedPassword);
    
    logger.debug('Password comparison completed', { isMatch });
    return isMatch;
  } catch (error) {
    logger.errorWithContext(error, {}, 'Error comparing password');
    throw error;
  }
};

/**
 * Validate password strength
 * @param {String} password - Plain text password
 * @returns {Object} Validation result
 */
const validatePasswordStrength = (password) => {
  const result = {
    isValid: true,
    errors: [],
    strength: 'weak',
    score: 0
  };

  if (!password) {
    result.isValid = false;
    result.errors.push('Password is required');
    return result;
  }

  if (typeof password !== 'string') {
    result.isValid = false;
    result.errors.push('Password must be a string');
    return result;
  }

  // Length check
  if (password.length < 8) {
    result.isValid = false;
    result.errors.push('Password must be at least 8 characters long');
  } else if (password.length >= 8) {
    result.score += 1;
  }

  if (password.length >= 12) {
    result.score += 1;
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one lowercase letter');
  } else {
    result.score += 1;
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one uppercase letter');
  } else {
    result.score += 1;
  }

  // Number check
  if (!/\d/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one number');
  } else {
    result.score += 1;
  }

  // Special character check
  if (!/[@$!%*?&]/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one special character (@$!%*?&)');
  } else {
    result.score += 1;
  }

  // Common password check
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', 'monkey',
    'dragon', 'master', 'shadow', 'superman', 'michael'
  ];

  if (commonPasswords.includes(password.toLowerCase())) {
    result.isValid = false;
    result.errors.push('Password is too common, please choose a more secure password');
    result.score = Math.max(0, result.score - 2);
  }

  // Sequential characters check
  if (/123|abc|qwe|asd|zxc/i.test(password)) {
    result.errors.push('Avoid using sequential characters');
    result.score = Math.max(0, result.score - 1);
  }

  // Repeated characters check
  if (/(.)\1{2,}/.test(password)) {
    result.errors.push('Avoid using repeated characters');
    result.score = Math.max(0, result.score - 1);
  }

  // Determine strength based on score
  if (result.score >= 6) {
    result.strength = 'very strong';
  } else if (result.score >= 5) {
    result.strength = 'strong';
  } else if (result.score >= 3) {
    result.strength = 'medium';
  } else if (result.score >= 1) {
    result.strength = 'weak';
  } else {
    result.strength = 'very weak';
  }

  return result;
};

/**
 * Generate a random password
 * @param {Number} length - Password length (default: 12)
 * @param {Object} options - Generation options
 * @returns {String} Generated password
 */
const generateRandomPassword = (length = 12, options = {}) => {
  const defaults = {
    includeLowercase: true,
    includeUppercase: true,
    includeNumbers: true,
    includeSpecialChars: true,
    excludeSimilar: true, // Exclude similar looking characters (0, O, l, 1, etc.)
    excludeAmbiguous: true // Exclude ambiguous characters
  };

  const settings = { ...defaults, ...options };

  let charset = '';
  
  if (settings.includeLowercase) {
    charset += settings.excludeSimilar ? 'abcdefghijkmnopqrstuvwxyz' : 'abcdefghijklmnopqrstuvwxyz';
  }
  
  if (settings.includeUppercase) {
    charset += settings.excludeSimilar ? 'ABCDEFGHJKLMNPQRSTUVWXYZ' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  }
  
  if (settings.includeNumbers) {
    charset += settings.excludeSimilar ? '23456789' : '0123456789';
  }
  
  if (settings.includeSpecialChars) {
    charset += settings.excludeAmbiguous ? '@$!%*?&' : '@$!%*?&^#()_+-=[]{}|;:,.<>';
  }

  if (!charset) {
    throw new Error('At least one character type must be included');
  }

  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  // Ensure password meets minimum requirements
  const validation = validatePasswordStrength(password);
  if (!validation.isValid) {
    // Regenerate if it doesn't meet requirements (recursive call with limit)
    return generateRandomPassword(length, options);
  }

  return password;
};

/**
 * Hash multiple passwords in batch
 * @param {Array<String>} passwords - Array of plain text passwords
 * @returns {Promise<Array<String>>} Array of hashed passwords
 */
const hashPasswordsBatch = async (passwords) => {
  try {
    if (!Array.isArray(passwords)) {
      throw new Error('Passwords must be an array');
    }

    const hashedPasswords = await Promise.all(
      passwords.map(password => hashPassword(password))
    );

    logger.debug(`Batch hashed ${passwords.length} passwords`);
    return hashedPasswords;
  } catch (error) {
    logger.errorWithContext(error, { count: passwords?.length }, 'Error batch hashing passwords');
    throw error;
  }
};

/**
 * Check if a string is already hashed
 * @param {String} str - String to check
 * @returns {Boolean} Is hashed
 */
const isHashed = (str) => {
  if (!str || typeof str !== 'string') return false;
  
  // bcrypt hashes start with $2a$, $2b$, or $2y$ followed by cost and salt
  return /^\$2[aby]\$\d{2}\$.{53}$/.test(str);
};

/**
 * Get password hash info
 * @param {String} hashedPassword - Hashed password
 * @returns {Object} Hash information
 */
const getHashInfo = (hashedPassword) => {
  try {
    if (!isHashed(hashedPassword)) {
      throw new Error('Not a valid bcrypt hash');
    }

    const parts = hashedPassword.split('$');
    return {
      algorithm: parts[1], // 2a, 2b, or 2y
      cost: parseInt(parts[2]), // Salt rounds
      salt: parts[3], // Salt
      hash: parts[4] // Hash
    };
  } catch (error) {
    logger.errorWithContext(error, {}, 'Error getting hash info');
    throw error;
  }
};

module.exports = {
  hashPassword,
  comparePassword,
  validatePasswordStrength,
  generateRandomPassword,
  hashPasswordsBatch,
  isHashed,
  getHashInfo
};
